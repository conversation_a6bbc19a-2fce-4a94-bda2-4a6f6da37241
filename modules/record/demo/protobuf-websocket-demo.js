const { app, BrowserWindow } = require('electron');
const ProtobufWebSocketServer = require('../src/protobuf-websocket-server');
const ProtobufMessageHandler = require('../src/protobuf-message-handler');

let mainWindow;
let protobufServer;
let protobufHandler;

/**
 * Protobuf WebSocket 服务器演示
 * 展示如何使用protobuf格式进行WebSocket通信
 */

// 创建主窗口
async function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });

    // 可选：打开开发者工具
    // mainWindow.webContents.openDevTools();

    console.log('Main window created');
}

// 启动Protobuf WebSocket服务器
async function startProtobufWebSocketServer() {
    try {
        // 创建服务器实例
        protobufServer = new ProtobufWebSocketServer(8086, {
            enableProtobuf: true,
            enableJson: true,
            defaultFormat: 'protobuf'
        });

        // 创建消息处理器用于测试
        protobufHandler = new ProtobufMessageHandler();
        await protobufHandler.initialize();

        // 设置事件监听器
        setupServerEventListeners();

        // 启动服务器
        await protobufServer.start();

        console.log('🚀 Protobuf WebSocket Server started successfully!');
        console.log('📡 Server is listening on port 8086');
        console.log('🔧 Supported formats: protobuf, json');
        console.log('⚡ Default format: protobuf');
        console.log('');
        console.log('You can now connect clients using:');
        console.log('- Protobuf binary messages');
        console.log('- JSON text messages');
        console.log('- Mixed format (server auto-detects)');

        // 启动演示任务
        startDemoTasks();

    } catch (error) {
        console.error('❌ Failed to start Protobuf WebSocket Server:', error);
    }
}

// 设置服务器事件监听器
function setupServerEventListeners() {
    protobufServer.on('started', (port) => {
        console.log(`✅ Server started on port ${port}`);
    });

    protobufServer.on('client_info', (clientInfo, data) => {
        console.log(`📋 Client info received from ${clientInfo.id}:`, data);
    });

    protobufServer.on('live_events', (clientInfo, data) => {
        console.log(`🎬 Live events received from ${clientInfo.id}:`, {
            connectionId: data.connection_id || data.connectionId,
            liveUrl: data.live_url || data.liveUrl,
            eventsCount: data.data?.events_data?.length || data.data?.events_data?.length || 0
        });
    });

    protobufServer.on('log', (clientInfo, data) => {
        console.log(`📝 Log message from ${clientInfo.id}:`, data.message || data);
    });

    protobufServer.on('heartbeat', (clientInfo, data) => {
        console.log(`💓 Heartbeat from ${clientInfo.id}:`, {
            totalConnections: data.total_connections || data.totalConnections
        });
    });

    protobufServer.on('custom_message', (clientInfo, data) => {
        console.log(`🔧 Custom message from ${clientInfo.id}:`, data.type || 'unknown');
    });

    protobufServer.on('client_disconnected', (clientInfo) => {
        console.log(`👋 Client disconnected: ${clientInfo.id}`);
    });
}

// 启动演示任务
function startDemoTasks() {
    // 每30秒广播服务器状态
    setInterval(() => {
        const stats = protobufServer.getStats();
        protobufServer.broadcast('status_info', {
            server_stats: {
                uptime: Date.now() - (stats.startTime || Date.now()),
                total_clients: stats.totalConnections,
                active_connections: stats.activeConnections,
                total_messages: stats.messagesReceived + stats.messagesSent,
                message_counts: {
                    received: stats.messagesReceived.toString(),
                    sent: stats.messagesSent.toString(),
                    protobuf: stats.protobufMessages.toString(),
                    json: stats.jsonMessages.toString(),
                    errors: stats.errors.toString()
                }
            },
            client_stats: stats.clients.map(client => ({
                client_id: client.id,
                connected_time: Date.now() - client.connectedAt.getTime(),
                total_connections: 1,
                total_events: client.stats.messagesReceived,
                event_counts: {
                    received: client.stats.messagesReceived.toString(),
                    sent: client.stats.messagesSent.toString(),
                    protobuf: client.stats.protobufMessages.toString(),
                    json: client.stats.jsonMessages.toString()
                }
            }))
        });
    }, 30000);

    // 每60秒发送测试消息
    setInterval(() => {
        if (protobufServer.clients.size > 0) {
            protobufServer.broadcast('broadcast', {
                broadcast_type: 'server_announcement',
                data: Buffer.from(JSON.stringify({
                    message: 'This is a test broadcast from the Protobuf WebSocket Server',
                    timestamp: new Date().toISOString(),
                    activeClients: protobufServer.clients.size
                })),
                metadata: {
                    type: 'announcement',
                    priority: 'low'
                }
            });
        }
    }, 60000);

    // 演示protobuf消息创建
    setTimeout(() => {
        demonstrateProtobufMessages();
    }, 5000);
}

// 演示protobuf消息创建和序列化
async function demonstrateProtobufMessages() {
    console.log('\n🧪 Demonstrating Protobuf message creation...\n');

    try {
        // 1. 创建客户端信息消息
        const clientInfoMsg = protobufHandler.createClientMessage('client_info', {
            client_id: 'demo-client-001',
            version: '1.0.0',
            capabilities: {
                'live_events': 'true',
                'protobuf': 'true',
                'json': 'true'
            },
            status: 'CLIENT_STATUS_CONNECTED'
        });

        const clientInfoBuffer = protobufHandler.serialize(clientInfoMsg, 'client', 'demo-context');
        console.log('✅ Client info message serialized:', clientInfoBuffer.length, 'bytes');

        // 2. 创建直播事件消息
        const liveEventsMsg = protobufHandler.createClientMessage('live_events', {
            connection_id: 'conn-001',
            live_url: 'https://live.douyin.com/123456',
            data: {
                events_data: [
                    {
                        msg_type: 2,
                        msg_type_str: 'comment_msg',
                        comment_msg: {
                            user: {
                                user_name: 'test_user',
                                nick_name: '测试用户',
                                level: 5,
                                head_url: 'https://example.com/avatar.jpg'
                            },
                            content: '这是一条测试评论',
                            language: 'zh-CN'
                        }
                    }
                ],
                like_total: 100,
                watching_total: 500,
                timestamp: Date.now(),
                live_platform_type: 'douyin',
                context: 'demo-context'
            }
        });

        const liveEventsBuffer = protobufHandler.serialize(liveEventsMsg, 'client', 'demo-context');
        console.log('✅ Live events message serialized:', liveEventsBuffer.length, 'bytes');

        // 3. 创建服务器消息
        const welcomeMsg = protobufHandler.createServerMessage('welcome', {
            server_version: '1.0.0',
            message: 'Welcome to Protobuf WebSocket Server',
            supported_features: ['protobuf', 'json', 'mixed-mode']
        });

        const welcomeBuffer = protobufHandler.serialize(welcomeMsg, 'server', 'demo-context');
        console.log('✅ Welcome message serialized:', welcomeBuffer.length, 'bytes');

        // 4. 演示反序列化
        const deserializedWelcome = protobufHandler.deserialize(welcomeBuffer);
        console.log('✅ Welcome message deserialized:', deserializedWelcome.serverMessage.welcome.message);

        // 5. 演示JSON到Protobuf转换
        const jsonMessage = {
            type: 'live_events',
            connectionId: 'conn-002',
            liveUrl: 'https://live.kuaishou.com/u/test',
            data: {
                events_data: [{
                    msg_type: 1,
                    msg_type_str: 'like_msg',
                    like_msg: {
                        user: {
                            user_name: 'like_user',
                            nick_name: '点赞用户',
                            level: 3
                        },
                        like_count: 5
                    }
                }],
                like_total: 200,
                watching_total: 800,
                timestamp: Date.now(),
                live_platform_type: 'kuaishou'
            }
        };

        const convertedMsg = protobufHandler.convertJsonToProtobuf(jsonMessage, 'client');
        const convertedBuffer = protobufHandler.serialize(convertedMsg, 'client', 'demo-context');
        console.log('✅ JSON to Protobuf conversion successful:', convertedBuffer.length, 'bytes');

        console.log('\n🎉 Protobuf demonstration completed successfully!\n');

    } catch (error) {
        console.error('❌ Protobuf demonstration failed:', error);
    }
}

// Electron应用事件
app.whenReady().then(async () => {
    await createWindow();
    await startProtobufWebSocketServer();
});

app.on('window-all-closed', () => {
    if (protobufServer) {
        protobufServer.stop();
    }
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        await createWindow();
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Protobuf WebSocket Server...');
    if (protobufServer) {
        protobufServer.stop();
    }
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down Protobuf WebSocket Server...');
    if (protobufServer) {
        protobufServer.stop();
    }
    process.exit(0);
});

console.log('🚀 Starting Protobuf WebSocket Demo...');
console.log('📋 This demo will:');
console.log('   1. Start a WebSocket server supporting both Protobuf and JSON');
console.log('   2. Auto-detect message formats');
console.log('   3. Demonstrate message serialization/deserialization');
console.log('   4. Show format conversion capabilities');
console.log('   5. Broadcast periodic status updates');
console.log('');
