# LiveApi WebSocket 客户端模式

这个文档介绍了LiveWebSocketBridge的新客户端模式，它将原有的架构反转：客户端（Electron应用）连接到远程服务器，而不是作为服务器等待连接。

## 架构对比

### 原有架构（服务器模式）
```
[外部客户端] --连接--> [Electron应用(WebSocket服务器)] --监控--> [直播间]
```

### 新架构（客户端模式）
```
[远程服务器] <--连接-- [Electron应用(WebSocket客户端)] --监控--> [直播间]
```

## 功能特性

- 🔄 **反转架构** - 客户端连接到远程服务器
- 📡 **实时事件同步** - 将直播间事件实时发送到远程服务器
- 🎯 **服务器控制** - 远程服务器可以控制客户端连接/断开直播间
- 🪟 **独立窗口** - 每个直播间仍然创建独立的Electron窗口
- 💓 **心跳检测** - 自动重连机制
- 📊 **状态同步** - 实时同步客户端状态到服务器
- 🔧 **灵活配置** - 可配置远程服务器地址

## 快速开始

### 1. 启动远程服务器

```bash
# 启动远程WebSocket服务器
npm run start:remote-server
```

这将启动一个WebSocket服务器在端口8084，等待客户端连接。

### 2. 启动客户端

```bash
# 启动WebSocket客户端模式的LiveApi
npm run start:websocket-client
```

这将：
- 创建一个Electron应用
- 连接到远程WebSocket服务器
- 等待服务器发送直播间连接请求
- 为每个直播间创建独立的Electron窗口
- 将拦截到的数据发送给远程服务器

## 使用方法

### 服务器端命令

在远程服务器的控制台中，你可以使用以下命令：

```bash
# 查看连接的客户端
clients

# 请求客户端连接到直播间
connect <clientId> <liveUrl>
# 例如: connect abc123 https://live.kuaishou.com/u/xxxxx

# 请求客户端断开连接
disconnect <clientId> [connectionId]
# 例如: disconnect abc123 (断开所有连接)
# 例如: disconnect abc123 def456 (断开特定连接)

# 获取客户端状态
status <clientId>

# 广播消息给所有客户端
broadcast <message>

# 退出服务器
quit
```

### 客户端配置

在客户端代码中，你可以配置远程服务器地址：

```javascript
const LiveWebSocketBridge = require('./modules/record/src/live-websocket-bridge');

// 创建客户端模式的WebSocket桥接器
const bridge = new LiveWebSocketBridge('my-context', 8084, false, 'client');

// 设置远程服务器地址（可选，默认为 ws://localhost:8084）
bridge.setServerUrl('ws://your-remote-server:8084');

// 启动客户端
bridge.startServer();
```

## 消息协议

### 客户端发送给服务器的消息

#### 1. 客户端信息
```json
{
  "type": "client_info",
  "context": "client-context",
  "timestamp": 1234567890
}
```

#### 2. 直播事件数据
```json
{
  "type": "live_events",
  "connectionId": "connection-id",
  "liveUrl": "https://live.kuaishou.com/u/xxxxx",
  "data": {
    "events_data": [...],
    "like_total": 100,
    "watching_total": 500
  },
  "timestamp": 1234567890,
  "source": "liveApi",
  "context": "client-context"
}
```

#### 3. 日志消息
```json
{
  "type": "log",
  "connectionId": "connection-id",
  "liveUrl": "https://live.kuaishou.com/u/xxxxx",
  "message": "log message",
  "timestamp": 1234567890,
  "source": "liveApi",
  "context": "client-context"
}
```

#### 4. 心跳消息
```json
{
  "type": "heartbeat",
  "context": "client-context",
  "totalConnections": 2,
  "timestamp": 1234567890
}
```

### 服务器发送给客户端的消息

#### 1. 连接直播间请求
```json
{
  "type": "live_room_request",
  "requestId": "request-id",
  "liveUrl": "https://live.kuaishou.com/u/xxxxx",
  "options": {},
  "timestamp": 1234567890
}
```

#### 2. 断开直播间请求
```json
{
  "type": "disconnect_room_request",
  "requestId": "request-id",
  "connectionId": "connection-id", // 可选，不提供则断开所有连接
  "timestamp": 1234567890
}
```

#### 3. 状态请求
```json
{
  "type": "get_status_request",
  "requestId": "request-id",
  "timestamp": 1234567890
}
```

## 代码示例

### 完整的客户端示例

```javascript
const { app, BrowserWindow } = require('electron');
const LiveWebSocketBridge = require('./modules/record/src/live-websocket-bridge');

let liveWebSocketBridge;

app.whenReady().then(() => {
    // 创建客户端模式的WebSocket桥接器
    liveWebSocketBridge = new LiveWebSocketBridge('my-client-context', 8084, false, 'client');
    
    // 可选：设置远程服务器地址
    // liveWebSocketBridge.setServerUrl('ws://remote-server:8084');
    
    // 启动客户端
    liveWebSocketBridge.startServer();
});

app.on('before-quit', () => {
    if (liveWebSocketBridge) {
        liveWebSocketBridge.release();
    }
});
```

### 完整的服务器示例

```javascript
const RemoteWebSocketServer = require('./modules/record/demo/remote-server-demo');

const server = new RemoteWebSocketServer(8084);
server.start();

// 请求客户端连接到直播间
setTimeout(() => {
    const clients = server.getClientsInfo();
    if (clients.length > 0) {
        const clientId = clients[0].id;
        server.requestLiveRoom(clientId, 'https://live.kuaishou.com/u/xxxxx');
    }
}, 5000);
```

## 优势

1. **集中管理** - 远程服务器可以集中管理多个客户端
2. **动态控制** - 服务器可以动态控制客户端连接哪些直播间
3. **数据汇聚** - 所有直播数据汇聚到远程服务器
4. **负载分散** - 可以将不同直播间分配给不同客户端
5. **监控便利** - 服务器端可以监控所有客户端的状态

## 注意事项

1. **网络连接** - 确保客户端能够访问远程服务器
2. **防火墙** - 检查防火墙设置，确保端口开放
3. **重连机制** - 客户端会自动尝试重连，但有最大重试次数限制
4. **资源管理** - 每个直播间会创建独立窗口，注意资源使用

## 故障排除

### 连接失败
- 检查服务器是否正在运行
- 检查网络连接
- 检查防火墙设置
- 验证服务器地址和端口

### 重连问题
- 客户端会自动重连，最多尝试5次
- 可以通过修改 `maxReconnectAttempts` 调整重试次数
- 重连间隔为3秒，可以通过 `reconnectDelay` 调整

### 窗口管理
- 每个直播间创建独立窗口
- 窗口会在连接断开时自动关闭
- 可以通过服务器命令控制窗口显示/隐藏
