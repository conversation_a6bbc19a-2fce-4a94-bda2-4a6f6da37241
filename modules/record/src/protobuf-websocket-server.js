const WebSocket = require('ws');
const EventEmitter = require('events');
const ProtobufMessageHandler = require('./protobuf-message-handler');

/**
 * 支持Protobuf的WebSocket服务器
 * 可以同时处理JSON和Protobuf格式的消息
 */
class ProtobufWebSocketServer extends EventEmitter {
    constructor(port = 8084, options = {}) {
        super();
        this.port = port;
        this.options = {
            enableProtobuf: true,
            enableJson: true,
            defaultFormat: 'protobuf', // 'protobuf' 或 'json'
            ...options
        };
        
        this.wss = null;
        this.clients = new Set();
        this.isRunning = false;
        this.protobufHandler = new ProtobufMessageHandler();
        this.stats = {
            totalConnections: 0,
            activeConnections: 0,
            messagesReceived: 0,
            messagesSent: 0,
            protobufMessages: 0,
            jsonMessages: 0,
            errors: 0
        };
    }

    /**
     * 启动服务器
     */
    async start() {
        if (this.isRunning) {
            console.log('ProtobufWebSocket server is already running');
            return;
        }

        try {
            // 初始化protobuf处理器
            if (this.options.enableProtobuf) {
                await this.protobufHandler.initialize();
                console.log('Protobuf message handler initialized');
            }

            // 创建WebSocket服务器
            this.wss = new WebSocket.Server({ 
                port: this.port,
                perMessageDeflate: false // 禁用压缩以避免与protobuf冲突
            });
            this.isRunning = true;

            this.wss.on('connection', (ws, req) => {
                this.handleNewConnection(ws, req);
            });

            this.wss.on('error', (error) => {
                console.error('ProtobufWebSocket Server error:', error);
                this.stats.errors++;
            });

            console.log(`ProtobufWebSocket server started on port ${this.port}`);
            console.log(`Supported formats: ${this.getSupportedFormats().join(', ')}`);
            console.log(`Default format: ${this.options.defaultFormat}`);
            
            this.emit('started', this.port);
        } catch (error) {
            console.error('Failed to start ProtobufWebSocket server:', error);
            throw error;
        }
    }

    /**
     * 处理新连接
     */
    handleNewConnection(ws, req) {
        const clientInfo = {
            id: this.generateClientId(),
            remoteAddress: req.socket.remoteAddress,
            userAgent: req.headers['user-agent'],
            connectedAt: new Date(),
            messageFormat: this.options.defaultFormat,
            subscriptions: new Set(),
            stats: {
                messagesReceived: 0,
                messagesSent: 0,
                protobufMessages: 0,
                jsonMessages: 0
            }
        };

        ws.clientInfo = clientInfo;
        this.clients.add(ws);
        this.stats.totalConnections++;
        this.stats.activeConnections++;

        console.log(`New connection: ${clientInfo.id} from ${clientInfo.remoteAddress}`);

        // 发送欢迎消息
        this.sendWelcomeMessage(ws);

        // 设置事件监听器
        ws.on('message', (data) => this.handleMessage(ws, data));
        ws.on('close', () => this.handleDisconnection(ws));
        ws.on('error', (error) => this.handleError(ws, error));
        ws.on('pong', () => this.handlePong(ws));

        // 开始心跳检测
        this.startHeartbeat(ws);
    }

    /**
     * 处理消息
     */
    async handleMessage(ws, data) {
        try {
            this.stats.messagesReceived++;
            ws.clientInfo.stats.messagesReceived++;
            ws.clientInfo.lastActivity = new Date();

            let message;
            let isProtobuf = false;

            // 尝试检测消息格式
            if (Buffer.isBuffer(data)) {
                // 二进制数据，尝试作为protobuf解析
                if (this.options.enableProtobuf) {
                    try {
                        message = this.protobufHandler.deserialize(data);
                        isProtobuf = true;
                        this.stats.protobufMessages++;
                        ws.clientInfo.stats.protobufMessages++;
                        ws.clientInfo.messageFormat = 'protobuf';
                    } catch (error) {
                        console.warn('Failed to parse as protobuf, trying JSON:', error.message);
                    }
                }
                
                // 如果protobuf解析失败，尝试作为JSON解析
                if (!message && this.options.enableJson) {
                    try {
                        message = JSON.parse(data.toString());
                        this.stats.jsonMessages++;
                        ws.clientInfo.stats.jsonMessages++;
                        ws.clientInfo.messageFormat = 'json';
                    } catch (error) {
                        throw new Error('Unable to parse message as protobuf or JSON');
                    }
                }
            } else {
                // 文本数据，作为JSON解析
                if (this.options.enableJson) {
                    message = JSON.parse(data);
                    this.stats.jsonMessages++;
                    ws.clientInfo.stats.jsonMessages++;
                    ws.clientInfo.messageFormat = 'json';
                } else {
                    throw new Error('JSON format is disabled');
                }
            }

            if (!message) {
                throw new Error('No message parsed');
            }

            console.log(`Received ${isProtobuf ? 'protobuf' : 'json'} message from ${ws.clientInfo.id}:`, 
                       isProtobuf ? message.payload : message);

            // 处理消息
            await this.processMessage(ws, message, isProtobuf);

        } catch (error) {
            console.error('Error handling message:', error);
            this.stats.errors++;
            this.sendErrorMessage(ws, error.message);
        }
    }

    /**
     * 处理具体的消息内容
     */
    async processMessage(ws, message, isProtobuf) {
        let messageType, messageData;

        if (isProtobuf) {
            // Protobuf消息结构
            if (message.clientMessage) {
                const clientMsg = message.clientMessage;
                messageType = Object.keys(clientMsg)[0]; // 获取消息类型
                messageData = clientMsg[messageType];
            } else if (message.serverMessage) {
                const serverMsg = message.serverMessage;
                messageType = Object.keys(serverMsg)[0];
                messageData = serverMsg[messageType];
            } else {
                throw new Error('Unknown protobuf message structure');
            }
        } else {
            // JSON消息结构
            messageType = message.type;
            messageData = message;
        }

        // 根据消息类型处理
        switch (messageType) {
            case 'client_info':
                await this.handleClientInfo(ws, messageData, isProtobuf);
                break;
            case 'live_events':
                await this.handleLiveEvents(ws, messageData, isProtobuf);
                break;
            case 'log':
                await this.handleLog(ws, messageData, isProtobuf);
                break;
            case 'heartbeat':
                await this.handleHeartbeat(ws, messageData, isProtobuf);
                break;
            case 'ping':
                await this.handlePing(ws, messageData, isProtobuf);
                break;
            case 'subscribe':
                await this.handleSubscribe(ws, messageData, isProtobuf);
                break;
            case 'unsubscribe':
                await this.handleUnsubscribe(ws, messageData, isProtobuf);
                break;
            case 'custom':
                await this.handleCustomMessage(ws, messageData, isProtobuf);
                break;
            default:
                console.warn(`Unknown message type: ${messageType}`);
                this.sendErrorMessage(ws, `Unknown message type: ${messageType}`);
        }
    }

    /**
     * 发送消息给客户端
     */
    async sendMessage(ws, messageType, data, forceFormat = null) {
        try {
            const format = forceFormat || ws.clientInfo.messageFormat || this.options.defaultFormat;
            let messageBuffer;

            if (format === 'protobuf' && this.options.enableProtobuf) {
                // 发送protobuf格式
                const protobufMessage = this.protobufHandler.createServerMessage(messageType, data);
                messageBuffer = this.protobufHandler.serialize(protobufMessage, 'server', ws.clientInfo.id);
            } else if (format === 'json' && this.options.enableJson) {
                // 发送JSON格式
                const jsonMessage = {
                    type: messageType,
                    timestamp: Date.now(),
                    source: 'server',
                    ...data
                };
                messageBuffer = Buffer.from(JSON.stringify(jsonMessage));
            } else {
                throw new Error(`Unsupported message format: ${format}`);
            }

            if (ws.readyState === WebSocket.OPEN) {
                ws.send(messageBuffer);
                this.stats.messagesSent++;
                ws.clientInfo.stats.messagesSent++;
                
                if (format === 'protobuf') {
                    ws.clientInfo.stats.protobufMessages++;
                } else {
                    ws.clientInfo.stats.jsonMessages++;
                }
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.stats.errors++;
        }
    }

    /**
     * 广播消息给所有客户端
     */
    async broadcast(messageType, data, filter = null) {
        const promises = [];
        
        this.clients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                if (!filter || filter(ws)) {
                    promises.push(this.sendMessage(ws, messageType, data));
                }
            }
        });

        await Promise.all(promises);
        console.log(`Broadcasted ${messageType} message to ${promises.length} clients`);
    }

    /**
     * 发送欢迎消息
     */
    async sendWelcomeMessage(ws) {
        await this.sendMessage(ws, 'welcome', {
            server_version: '1.0.0',
            message: 'Connected to ProtobufWebSocket Server',
            supported_features: this.getSupportedFormats()
        });
    }

    /**
     * 发送错误消息
     */
    async sendErrorMessage(ws, errorMessage) {
        await this.sendMessage(ws, 'error', {
            error_code: 'GENERAL_ERROR',
            error_message: errorMessage,
            timestamp: Date.now()
        });
    }

    /**
     * 处理客户端信息
     */
    async handleClientInfo(ws, data, isProtobuf) {
        console.log(`Client info from ${ws.clientInfo.id}:`, data);
        ws.clientInfo.clientData = data;
        this.emit('client_info', ws.clientInfo, data);
    }

    /**
     * 处理直播事件
     */
    async handleLiveEvents(ws, data, isProtobuf) {
        console.log(`Live events from ${ws.clientInfo.id}:`, data.connection_id || data.connectionId);
        this.emit('live_events', ws.clientInfo, data);
        
        // 转发给订阅了live_events的其他客户端
        await this.broadcast('live_events', data, (client) => {
            return client !== ws && client.clientInfo.subscriptions.has('live_events');
        });
    }

    /**
     * 处理日志消息
     */
    async handleLog(ws, data, isProtobuf) {
        console.log(`Log from ${ws.clientInfo.id}:`, data.message || data);
        this.emit('log', ws.clientInfo, data);
    }

    /**
     * 处理心跳消息
     */
    async handleHeartbeat(ws, data, isProtobuf) {
        ws.clientInfo.lastHeartbeat = new Date();
        this.emit('heartbeat', ws.clientInfo, data);
    }

    /**
     * 处理Ping消息
     */
    async handlePing(ws, data, isProtobuf) {
        await this.sendMessage(ws, 'pong', {
            ping_id: data.ping_id || data.pingId || ''
        });
    }

    /**
     * 处理订阅消息
     */
    async handleSubscribe(ws, data, isProtobuf) {
        const eventTypes = data.event_types || data.eventTypes || [];
        eventTypes.forEach(eventType => {
            ws.clientInfo.subscriptions.add(eventType);
        });

        await this.sendMessage(ws, 'subscribed', {
            event_types: Array.from(ws.clientInfo.subscriptions),
            subscription_id: ws.clientInfo.id
        });
    }

    /**
     * 处理取消订阅消息
     */
    async handleUnsubscribe(ws, data, isProtobuf) {
        const eventTypes = data.event_types || data.eventTypes || [];
        eventTypes.forEach(eventType => {
            ws.clientInfo.subscriptions.delete(eventType);
        });

        await this.sendMessage(ws, 'unsubscribed', {
            event_types: eventTypes,
            subscription_id: ws.clientInfo.id
        });
    }

    /**
     * 处理自定义消息
     */
    async handleCustomMessage(ws, data, isProtobuf) {
        console.log(`Custom message from ${ws.clientInfo.id}:`, data.type || 'unknown');
        this.emit('custom_message', ws.clientInfo, data);
    }

    /**
     * 处理断开连接
     */
    handleDisconnection(ws) {
        console.log(`Client disconnected: ${ws.clientInfo.id}`);
        this.clients.delete(ws);
        this.stats.activeConnections--;
        this.emit('client_disconnected', ws.clientInfo);
    }

    /**
     * 处理错误
     */
    handleError(ws, error) {
        console.error(`WebSocket error for client ${ws.clientInfo.id}:`, error);
        this.stats.errors++;
        this.clients.delete(ws);
        this.stats.activeConnections--;
    }

    /**
     * 处理Pong响应
     */
    handlePong(ws) {
        ws.clientInfo.lastPong = new Date();
    }

    /**
     * 开始心跳检测
     */
    startHeartbeat(ws) {
        const interval = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.ping();
            } else {
                clearInterval(interval);
            }
        }, 30000); // 30秒心跳

        ws.heartbeatInterval = interval;
    }

    /**
     * 停止服务器
     */
    stop() {
        if (!this.isRunning) {
            console.log('ProtobufWebSocket server is not running');
            return;
        }

        // 关闭所有客户端连接
        this.clients.forEach(ws => {
            if (ws.heartbeatInterval) {
                clearInterval(ws.heartbeatInterval);
            }
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        this.clients.clear();

        // 关闭服务器
        if (this.wss) {
            this.wss.close(() => {
                console.log('ProtobufWebSocket server stopped');
                this.emit('stopped');
            });
        }

        this.isRunning = false;
        this.stats.activeConnections = 0;
    }

    /**
     * 获取支持的格式
     */
    getSupportedFormats() {
        const formats = [];
        if (this.options.enableProtobuf) formats.push('protobuf');
        if (this.options.enableJson) formats.push('json');
        return formats;
    }

    /**
     * 生成客户端ID
     */
    generateClientId() {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            uptime: this.isRunning ? Date.now() - this.startTime : 0,
            clients: Array.from(this.clients).map(ws => ({
                id: ws.clientInfo.id,
                connectedAt: ws.clientInfo.connectedAt,
                messageFormat: ws.clientInfo.messageFormat,
                subscriptions: Array.from(ws.clientInfo.subscriptions),
                stats: ws.clientInfo.stats
            }))
        };
    }
}

module.exports = ProtobufWebSocketServer;
