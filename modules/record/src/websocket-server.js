const WebSocket = require('ws');
const EventEmitter = require('events');

class WebSocketServer extends EventEmitter {
    constructor(port = 8084) {
        super();
        this.port = port;
        this.wss = null;
        this.clients = new Set();
        this.isRunning = false;
    }

    start() {
        if (this.isRunning) {
            console.log('WebSocket server is already running');
            return;
        }

        this.wss = new WebSocket.Server({ port: this.port });
        this.isRunning = true;

        this.wss.on('connection', (ws, req) => {
            console.log(`New WebSocket connection from ${req.socket.remoteAddress}`);
            this.clients.add(ws);

            // 发送欢迎消息
            ws.send(JSON.stringify({
                type: 'welcome',
                message: 'Connected to LiveApi WebSocket Server',
                timestamp: new Date().getTime()
            }));

            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    console.log('Received message from client:', data);
                    
                    // 处理客户端消息
                    this.handleClientMessage(ws, data);
                } catch (error) {
                    console.error('Error parsing client message:', error);
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: 'Invalid JSON format',
                        timestamp: new Date().getTime()
                    }));
                }
            });

            ws.on('close', () => {
                console.log('Client disconnected');
                this.clients.delete(ws);
            });

            ws.on('error', (error) => {
                console.error('WebSocket error:', error);
                this.clients.delete(ws);
            });
        });

        this.wss.on('error', (error) => {
            console.error('WebSocket Server error:', error);
        });

        console.log(`WebSocket server started on port ${this.port}`);
        this.emit('started', this.port);
    }

    stop() {
        if (!this.isRunning) {
            console.log('WebSocket server is not running');
            return;
        }

        // 关闭所有客户端连接
        this.clients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        this.clients.clear();

        // 关闭服务器
        if (this.wss) {
            this.wss.close(() => {
                console.log('WebSocket server stopped');
                this.emit('stopped');
            });
        }

        this.isRunning = false;
    }

    // 广播消息给所有连接的客户端
    broadcast(data) {
        if (!this.isRunning) {
            console.warn('Cannot broadcast: WebSocket server is not running');
            return;
        }

        const message = JSON.stringify(data);
        let sentCount = 0;

        this.clients.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                try {
                    ws.send(message);
                    sentCount++;
                } catch (error) {
                    console.error('Error sending message to client:', error);
                    this.clients.delete(ws);
                }
            } else {
                // 清理已断开的连接
                this.clients.delete(ws);
            }
        });

        console.log(`Broadcasted message to ${sentCount} clients`);
    }

    // 发送消息给特定客户端
    sendToClient(ws, data) {
        if (ws.readyState === WebSocket.OPEN) {
            try {
                ws.send(JSON.stringify(data));
            } catch (error) {
                console.error('Error sending message to specific client:', error);
                this.clients.delete(ws);
            }
        }
    }

    // 处理客户端消息
    handleClientMessage(ws, data) {
        switch (data.type) {
            case 'ping':
                this.sendToClient(ws, {
                    type: 'pong',
                    timestamp: new Date().getTime()
                });
                break;

            case 'subscribe':
                // 客户端订阅特定类型的消息
                ws.subscriptions = ws.subscriptions || new Set();
                if (data.eventTypes && Array.isArray(data.eventTypes)) {
                    data.eventTypes.forEach(eventType => {
                        ws.subscriptions.add(eventType);
                    });
                }
                this.sendToClient(ws, {
                    type: 'subscribed',
                    eventTypes: Array.from(ws.subscriptions),
                    timestamp: new Date().getTime()
                });
                break;

            case 'unsubscribe':
                // 客户端取消订阅
                if (ws.subscriptions && data.eventTypes && Array.isArray(data.eventTypes)) {
                    data.eventTypes.forEach(eventType => {
                        ws.subscriptions.delete(eventType);
                    });
                }
                this.sendToClient(ws, {
                    type: 'unsubscribed',
                    eventTypes: data.eventTypes,
                    timestamp: new Date().getTime()
                });
                break;

            case 'connect_live':
                // 客户端请求连接直播间
                this.emit('connect_live_request', {
                    ws: ws,
                    liveUrl: data.liveUrl,
                    options: data.options || {}
                });
                break;

            case 'disconnect_live':
                // 客户端请求断开直播间连接
                this.emit('disconnect_live_request', {
                    ws: ws,
                    connectionId: data.connectionId // 可选，如果不提供则断开所有连接
                });
                break;

            case 'list_connections':
                // 客户端请求获取连接列表
                this.emit('list_connections_request', {
                    ws: ws
                });
                break;

            case 'disconnect_connection':
                // 客户端请求断开特定连接
                this.emit('disconnect_connection_request', {
                    ws: ws,
                    connectionId: data.connectionId
                });
                break;

            case 'toggle_window':
                // 客户端请求显示/隐藏窗口
                this.emit('toggle_window_request', {
                    ws: ws,
                    connectionId: data.connectionId,
                    visible: data.visible
                });
                break;

            case 'get_windows_info':
                // 客户端请求获取窗口信息
                this.emit('get_windows_info_request', {
                    ws: ws
                });
                break;

            case 'get_status':
                // 客户端请求获取当前状态
                this.emit('get_status_request', {
                    ws: ws
                });
                break;

            default:
                this.sendToClient(ws, {
                    type: 'error',
                    message: `Unknown message type: ${data.type}`,
                    timestamp: new Date().getTime()
                });
        }
    }

    // 获取连接的客户端数量
    getClientCount() {
        return this.clients.size;
    }

    // 获取服务器状态
    getStatus() {
        return {
            isRunning: this.isRunning,
            port: this.port,
            clientCount: this.getClientCount()
        };
    }
}

module.exports = WebSocketServer;
