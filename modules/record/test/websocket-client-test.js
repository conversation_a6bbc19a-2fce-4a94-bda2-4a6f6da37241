const LiveWebSocketBridge = require('../src/live-websocket-bridge');
const RemoteWebSocketServer = require('../demo/remote-server-demo');

// 简单的测试脚本，验证客户端模式是否工作
async function testClientMode() {
    console.log('🧪 开始测试WebSocket客户端模式...');
    
    // 1. 启动远程服务器
    console.log('1. 启动远程服务器...');
    const server = new RemoteWebSocketServer(8081); // 使用不同端口避免冲突
    server.start();
    
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 创建客户端
    console.log('2. 创建WebSocket客户端...');
    const client = new LiveWebSocketBridge('test-client-context', 8081, true, 'client');
    
    // 3. 连接到服务器
    console.log('3. 连接到远程服务器...');
    client.startServer();
    
    // 等待连接建立
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. 检查连接状态
    console.log('4. 检查连接状态...');
    const status = client.getWebSocketStatus();
    console.log('客户端状态:', status);
    
    if (status && status.isConnected) {
        console.log('✅ 客户端成功连接到服务器');
        
        // 5. 测试发送消息
        console.log('5. 测试发送消息到服务器...');
        client.sendToServer({
            type: 'test_message',
            message: 'Hello from test client',
            timestamp: new Date().getTime()
        });
        
        // 6. 测试广播消息
        console.log('6. 测试广播消息...');
        client.broadcast({
            test: 'broadcast message from client'
        });
        
        console.log('✅ 消息发送测试完成');
    } else {
        console.log('❌ 客户端连接失败');
    }
    
    // 7. 清理资源
    console.log('7. 清理测试资源...');
    setTimeout(() => {
        client.release();
        server.stop();
        console.log('🎉 测试完成！');
        process.exit(0);
    }, 3000);
}

// 运行测试
if (require.main === module) {
    testClientMode().catch(error => {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    });
}

module.exports = { testClientMode };
