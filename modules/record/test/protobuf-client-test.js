const WebSocket = require('ws');
const ProtobufMessageHandler = require('../src/protobuf-message-handler');

/**
 * Protobuf WebSocket 客户端测试
 * 测试与protobuf WebSocket服务器的通信
 */
class ProtobufWebSocketClient {
    constructor(serverUrl = 'ws://localhost:8086') {
        this.serverUrl = serverUrl;
        this.ws = null;
        this.protobufHandler = new ProtobufMessageHandler();
        this.isConnected = false;
        this.messageFormat = 'protobuf'; // 'protobuf' 或 'json'
        this.clientId = `test-client-${Date.now()}`;
        this.stats = {
            messagesSent: 0,
            messagesReceived: 0,
            protobufMessages: 0,
            jsonMessages: 0,
            errors: 0
        };
    }

    /**
     * 连接到服务器
     */
    async connect() {
        try {
            console.log(`🔌 Connecting to ${this.serverUrl}...`);
            
            // 初始化protobuf处理器
            await this.protobufHandler.initialize();
            console.log('✅ Protobuf handler initialized');

            // 创建WebSocket连接
            this.ws = new WebSocket(this.serverUrl);

            this.ws.on('open', () => {
                console.log('✅ Connected to server');
                this.isConnected = true;
                this.onConnected();
            });

            this.ws.on('message', (data) => {
                this.handleMessage(data);
            });

            this.ws.on('close', () => {
                console.log('❌ Disconnected from server');
                this.isConnected = false;
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                this.stats.errors++;
            });

        } catch (error) {
            console.error('❌ Failed to connect:', error);
            throw error;
        }
    }

    /**
     * 连接成功后的处理
     */
    async onConnected() {
        // 发送客户端信息
        await this.sendClientInfo();
        
        // 订阅事件
        await this.subscribe(['live_events', 'log', 'status_info', 'broadcast']);
        
        // 开始测试序列
        this.startTestSequence();
    }

    /**
     * 处理接收到的消息
     */
    async handleMessage(data) {
        try {
            this.stats.messagesReceived++;
            let message;
            let isProtobuf = false;

            // 尝试解析消息格式
            if (Buffer.isBuffer(data)) {
                try {
                    message = this.protobufHandler.deserialize(data);
                    isProtobuf = true;
                    this.stats.protobufMessages++;
                } catch (error) {
                    // 尝试作为JSON解析
                    message = JSON.parse(data.toString());
                    this.stats.jsonMessages++;
                }
            } else {
                message = JSON.parse(data);
                this.stats.jsonMessages++;
            }

            console.log(`📨 Received ${isProtobuf ? 'protobuf' : 'json'} message:`, 
                       this.getMessageSummary(message, isProtobuf));

            // 处理特定消息类型
            this.processReceivedMessage(message, isProtobuf);

        } catch (error) {
            console.error('❌ Error handling message:', error);
            this.stats.errors++;
        }
    }

    /**
     * 获取消息摘要
     */
    getMessageSummary(message, isProtobuf) {
        if (isProtobuf) {
            if (message.serverMessage) {
                const serverMsg = message.serverMessage;
                const messageType = Object.keys(serverMsg)[0];
                return { type: messageType, source: message.source };
            }
        } else {
            return { type: message.type, source: message.source };
        }
        return { type: 'unknown' };
    }

    /**
     * 处理接收到的消息
     */
    processReceivedMessage(message, isProtobuf) {
        let messageType;
        
        if (isProtobuf && message.serverMessage) {
            messageType = Object.keys(message.serverMessage)[0];
        } else {
            messageType = message.type;
        }

        switch (messageType) {
            case 'welcome':
                console.log('🎉 Received welcome message');
                break;
            case 'subscribed':
                console.log('✅ Subscription confirmed');
                break;
            case 'status_info':
                console.log('📊 Received status info');
                break;
            case 'broadcast':
                console.log('📢 Received broadcast message');
                break;
            case 'pong':
                console.log('🏓 Received pong');
                break;
            default:
                console.log(`📝 Received ${messageType} message`);
        }
    }

    /**
     * 发送消息
     */
    async sendMessage(messageType, data, format = null) {
        if (!this.isConnected) {
            console.warn('⚠️ Not connected to server');
            return;
        }

        try {
            const useFormat = format || this.messageFormat;
            let messageBuffer;

            if (useFormat === 'protobuf') {
                const protobufMessage = this.protobufHandler.createClientMessage(messageType, data);
                messageBuffer = this.protobufHandler.serialize(protobufMessage, 'client', this.clientId);
            } else {
                const jsonMessage = {
                    type: messageType,
                    timestamp: Date.now(),
                    source: 'client',
                    context: this.clientId,
                    ...data
                };
                messageBuffer = Buffer.from(JSON.stringify(jsonMessage));
            }

            this.ws.send(messageBuffer);
            this.stats.messagesSent++;
            
            if (useFormat === 'protobuf') {
                this.stats.protobufMessages++;
            } else {
                this.stats.jsonMessages++;
            }

            console.log(`📤 Sent ${useFormat} message: ${messageType}`);

        } catch (error) {
            console.error('❌ Error sending message:', error);
            this.stats.errors++;
        }
    }

    /**
     * 发送客户端信息
     */
    async sendClientInfo() {
        await this.sendMessage('client_info', {
            client_id: this.clientId,
            version: '1.0.0',
            capabilities: {
                'protobuf': 'true',
                'json': 'true',
                'live_events': 'true'
            },
            status: 'CLIENT_STATUS_CONNECTED'
        });
    }

    /**
     * 订阅事件
     */
    async subscribe(eventTypes) {
        await this.sendMessage('subscribe', {
            event_types: eventTypes,
            filters: {}
        });
    }

    /**
     * 发送心跳
     */
    async sendHeartbeat() {
        await this.sendMessage('heartbeat', {
            total_connections: 1,
            stats: {
                'messages_sent': this.stats.messagesSent.toString(),
                'messages_received': this.stats.messagesReceived.toString(),
                'protobuf_messages': this.stats.protobufMessages.toString(),
                'json_messages': this.stats.jsonMessages.toString()
            }
        });
    }

    /**
     * 发送Ping
     */
    async sendPing() {
        await this.sendMessage('ping', {
            ping_id: `ping-${Date.now()}`
        });
    }

    /**
     * 发送模拟直播事件
     */
    async sendMockLiveEvents() {
        const mockEvents = [
            {
                msg_type: 2,
                msg_type_str: 'comment_msg',
                comment_msg: {
                    user: {
                        user_name: 'test_user_001',
                        nick_name: '测试用户001',
                        level: 5,
                        head_url: 'https://example.com/avatar1.jpg'
                    },
                    content: '这是一条测试评论消息',
                    language: 'zh-CN'
                }
            },
            {
                msg_type: 1,
                msg_type_str: 'like_msg',
                like_msg: {
                    user: {
                        user_name: 'test_user_002',
                        nick_name: '测试用户002',
                        level: 3
                    },
                    like_count: 10
                }
            },
            {
                msg_type: 3,
                msg_type_str: 'gift_msg',
                gift_msg: {
                    user: {
                        user_name: 'test_user_003',
                        nick_name: '测试用户003',
                        level: 8
                    },
                    gift_id: 'gift_001',
                    gift_name: '玫瑰花',
                    gift_url: 'https://example.com/gift.png',
                    gift_price: '1',
                    count: 5,
                    batch_size: 1,
                    total_count: 5,
                    gift_msg_key: 'gift_key_001'
                }
            }
        ];

        await this.sendMessage('live_events', {
            connection_id: `conn-${this.clientId}`,
            live_url: 'https://live.douyin.com/test123',
            data: {
                events_data: mockEvents,
                like_total: 1000,
                watching_total: 5000,
                timestamp: Date.now(),
                live_platform_type: 'douyin',
                context: this.clientId
            }
        });
    }

    /**
     * 发送日志消息
     */
    async sendLog(level, message) {
        await this.sendMessage('log', {
            level: level,
            message: message,
            category: 'test',
            metadata: {
                'client_id': this.clientId,
                'timestamp': new Date().toISOString()
            }
        });
    }

    /**
     * 开始测试序列
     */
    startTestSequence() {
        console.log('🧪 Starting test sequence...\n');

        let testStep = 0;
        const tests = [
            () => this.sendHeartbeat(),
            () => this.sendPing(),
            () => this.sendMockLiveEvents(),
            () => this.sendLog('LOG_LEVEL_INFO', 'Test log message from client'),
            () => this.switchMessageFormat(),
            () => this.sendMockLiveEvents(), // 用JSON格式发送
            () => this.sendLog('LOG_LEVEL_WARN', 'Testing JSON format'),
            () => this.switchMessageFormat(), // 切换回protobuf
            () => this.sendHeartbeat()
        ];

        const runNextTest = () => {
            if (testStep < tests.length) {
                console.log(`🔬 Running test ${testStep + 1}/${tests.length}`);
                tests[testStep]();
                testStep++;
                setTimeout(runNextTest, 3000); // 每3秒执行一个测试
            } else {
                console.log('✅ All tests completed!');
                this.printStats();
            }
        };

        setTimeout(runNextTest, 2000); // 2秒后开始测试
    }

    /**
     * 切换消息格式
     */
    switchMessageFormat() {
        this.messageFormat = this.messageFormat === 'protobuf' ? 'json' : 'protobuf';
        console.log(`🔄 Switched message format to: ${this.messageFormat}`);
    }

    /**
     * 打印统计信息
     */
    printStats() {
        console.log('\n📊 Client Statistics:');
        console.log(`   Messages sent: ${this.stats.messagesSent}`);
        console.log(`   Messages received: ${this.stats.messagesReceived}`);
        console.log(`   Protobuf messages: ${this.stats.protobufMessages}`);
        console.log(`   JSON messages: ${this.stats.jsonMessages}`);
        console.log(`   Errors: ${this.stats.errors}`);
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 运行测试
async function runTest() {
    const client = new ProtobufWebSocketClient();
    
    try {
        await client.connect();
        
        // 10分钟后自动断开
        setTimeout(() => {
            console.log('\n⏰ Test timeout, disconnecting...');
            client.disconnect();
            process.exit(0);
        }, 10 * 60 * 1000);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down client...');
    process.exit(0);
});

console.log('🚀 Starting Protobuf WebSocket Client Test...');
console.log('📋 This test will:');
console.log('   1. Connect to the protobuf WebSocket server');
console.log('   2. Test both protobuf and JSON message formats');
console.log('   3. Send various types of messages');
console.log('   4. Demonstrate format switching');
console.log('   5. Show performance statistics');
console.log('');

runTest();
